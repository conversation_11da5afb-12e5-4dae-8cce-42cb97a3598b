{"name": "get-stream", "version": "5.2.0", "description": "Get a stream as a string, buffer, or array", "license": "MIT", "repository": "sindresorhus/get-stream", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "buffer-stream.js"], "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "array", "object"], "dependencies": {"pump": "^3.0.0"}, "devDependencies": {"@types/node": "^12.0.7", "ava": "^2.0.0", "into-stream": "^5.0.0", "tsd": "^0.7.2", "xo": "^0.24.0"}}