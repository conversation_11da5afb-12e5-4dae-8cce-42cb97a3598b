const { chromium } = require('playwright');
const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');
const net = require('net');
require('dotenv').config();

// File paths
const logFilePath = path.join(__dirname, 'activity_log.json');
const sitesFilePath = path.join(__dirname, 'sites.json');
const intentHtmlPath = path.join(__dirname, 'intent_prompt.html');

// Initialize data structures
let activityLog = [];
let sitesData = { safe: [], blacklist: [] };
let activeTabStartTime = {};
let previousSafeUrl = {};
let userIntent = '';
let popupSocket = null;

// Load environment variables
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
if (!GEMINI_API_KEY) {
  console.error('GEMINI_API_KEY not set in .env file');
  process.exit(1);
}

// Connect to Electron TCP server
async function connectToPopupServer() {
  return new Promise((resolve, reject) => {
    popupSocket = net.createConnection({ host: 'localhost', port: 3001 }, () => {
      console.log('Connected to Electron popup server');
      resolve();
    });
    popupSocket.on('error', (error) => {
      console.error('Popup server connection error:', error);
      reject(error);
    });
  });
}

// Send popup message
async function showPopup(message, type) {
  if (popupSocket && !popupSocket.destroyed) {
    popupSocket.write(JSON.stringify({ message, type }) + '\n');
    console.log(`Sent popup message: ${message} (${type})`);
  } else {
    console.log('Popup server not connected, skipping popup');
  }
}

// HTML for intent prompt
const intentPromptHtml = `
<!DOCTYPE html>
<html>
<head>
  <title>Study Intent</title>
  <style>
    body { font-family: Arial, sans-serif; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; }
    .container { text-align: center; }
    input { padding: 10px; width: 300px; margin: 10px; }
    button { padding: 10px 20px; cursor: pointer; }
  </style>
</head>
<body>
  <div class="container">
    <h2>Enter Your Study Intent</h2>
    <input type="text" id="intent" placeholder="e.g., Researching calculus" />
    <button onclick="submitIntent()">Submit</button>
    <script>
      function submitIntent() {
        const intent = document.getElementById('intent').value;
        if (intent) {
          localStorage.setItem('studyIntent', intent);
          window.location.href = 'about:blank';
        }
      }
    </script>
  </div>
</body>
</html>
`;

// Load existing logs and sites
async function loadExistingData() {
  try {
    const logData = await fs.readFile(logFilePath, 'utf8');
    activityLog = JSON.parse(logData);
  } catch (error) {
    console.log('No existing activity log found, starting fresh.');
    activityLog = [];
  }
  try {
    const sitesDataRaw = await fs.readFile(sitesFilePath, 'utf8');
    sitesData = JSON.parse(sitesDataRaw);
  } catch (error) {
    console.log('No existing sites data found, starting fresh.');
    sitesData = { safe: [], blacklist: [] };
  }
}

// Save data to files
async function saveLog() {
  try {
    await fs.writeFile(logFilePath, JSON.stringify(activityLog, null, 2));
    console.log('Activity log saved.');
  } catch (error) {
    console.error('Error saving activity log:', error);
  }
}

async function saveSites() {
  try {
    await fs.writeFile(sitesFilePath, JSON.stringify(sitesData, null, 2));
    console.log('Sites data saved.');
  } catch (error) {
    console.error('Error saving sites data:', error);
  }
}

// Generate tab ID
function generateTabId(page) {
  return page.url() + '-' + Date.now();
}

// Extract domain from URL
function getDomain(url) {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    return '';
  }
}

// Prompt for study intent using local HTML file
async function promptStudyIntent(page) {
  return new Promise(async (resolve, reject) => {
    try {
      // Write HTML to file
      await fs.writeFile(intentHtmlPath, intentPromptHtml);
      console.log('Intent prompt HTML written to', intentHtmlPath);

      // Navigate to file
      await page.goto(`file://${intentHtmlPath}`, { waitUntil: 'load' });
      console.log('Navigated to intent prompt page');

      // Wait for navigation to about:blank after submission
      await page.waitForURL('about:blank', { timeout: 60000 });
      console.log('Detected navigation to about:blank');

      // Retrieve intent from localStorage
      userIntent = await page.evaluate(() => localStorage.getItem('studyIntent') || 'General studying');
      console.log('Retrieved study intent:', userIntent);
      resolve();
    } catch (error) {
      console.error('Failed to capture study intent:', error);
      reject(error);
    }
  });
}

// Gemini API call with function calling
async function evaluateWithGemini(url, title, intent) {
  try {
    const response = await axios.post(
      'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent?key=' + GEMINI_API_KEY,
      {
        contents: [{
          parts: [{
            text: `Evaluate if this webpage is related to and aligns with the user's study intent: "${intent}". URL: ${url}, Title: ${title}. Respond with a JSON object: { "isSafe": boolean }`
          }]
        }],
        tools: [{
          functionDeclarations: [{
            name: 'evaluateStudyRelevance',
            description: 'Evaluate if a webpage is study-related and aligns with user intent',
            parameters: {
              type: 'object',
              properties: { isSafe: { type: 'boolean' } },
              required: ['isSafe']
            }
          }]
        }]
      },
      { headers: { 'Content-Type': 'application/json' } }
    );

    const functionCall = response.data.candidates[0].content.parts[0].functionCall;
    console.log('Gemini response:', functionCall);
    return functionCall.args.isSafe;
  } catch (error) {
    console.error('Gemini API error:', error.message);
    return false; // Default to unsafe on error
  }
}

// Main function
(async () => {
  // Load existing data
  await loadExistingData();

  // Connect to Electron popup server
  try {
    await connectToPopupServer();
  } catch (error) {
    console.error('Failed to connect to popup server, continuing without popups');
  }

  // Launch browser
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  let page = await context.newPage();

  // Prompt for study intent
  try {
    console.log('Prompting for study intent');
    await promptStudyIntent(page);
    console.log('Received study intent:', userIntent);
    await page.close();
  } catch (error) {
    console.error('Failed to capture study intent:', error);
    await browser.close();
    process.exit(1);
  } finally {
    // Clean up HTML file
    try {
      await fs.unlink(intentHtmlPath);
      console.log('Cleaned up intent prompt HTML file');
    } catch (error) {
      console.log('No intent prompt HTML file to clean up');
    }
  }

  // Open new tab for browsing
  page = await context.newPage();
  await page.goto('about:blank');

  // Track time and activity
  async function trackPageTime(page, tabId) {
    activeTabStartTime[tabId] = Date.now();
    page.on('close', async () => {
      const endTime = Date.now();
      const timeSpent = (endTime - activeTabStartTime[tabId]) / 1000;
      activityLog.push({
        tabId,
        url: page.url(),
        event: 'time_spent',
        timeSpentSeconds: timeSpent,
        timestamp: new Date().toISOString()
      });
      await saveLog();
      delete activeTabStartTime[tabId];
      delete previousSafeUrl[tabId];
    });
  }

  // Check URL against safe/blacklist
  async function checkAndEvaluateUrl(page, tabId, url) {
    const domain = getDomain(url);
    if (!domain) {
      console.log('Invalid domain for URL:', url);
      return;
    }

    if (sitesData.blacklist.includes(domain)) {
      activityLog.push({
        tabId,
        url,
        event: 'blacklisted',
        timestamp: new Date().toISOString()
      });
      await saveLog();
      const prevUrl = previousSafeUrl[tabId] || 'about:blank';
      console.log(`Blacklisted domain ${domain}, redirecting to ${prevUrl}`);
      await showPopup(`Site ${domain} is blacklisted!`, 'error');
      await page.goto(prevUrl);
      return;
    }

    if (sitesData.safe.includes(domain)) {
      previousSafeUrl[tabId] = url;
      activityLog.push({
        tabId,
        url,
        event: 'safe',
        timestamp: new Date().toISOString()
      });
      await saveLog();
      console.log(`Safe domain ${domain}, continuing`);
      await showPopup(`Site ${domain} is safe for studying.`, 'success');
      return;
    }

    // Wait 30 seconds to evaluate
    console.log(`Waiting 10 seconds to evaluate ${url}`);
    await showPopup(`Evaluating ${domain} for study relevance...`, 'info');
    await new Promise(resolve => setTimeout(resolve, 10000));
    if (page.isClosed() || page.url() !== url) {
      console.log(`Page closed or URL changed, skipping evaluation for ${url}`);
      return;
    }

    const title = await page.title();
    console.log(`Evaluating ${url} with title: ${title}`);
    const isSafe = await evaluateWithGemini(url, title, userIntent);

    if (isSafe) {
      sitesData.safe.push(domain);
      previousSafeUrl[tabId] = url;
      activityLog.push({
        tabId,
        url,
        event: 'safe',
        timestamp: new Date().toISOString()
      });
      console.log(`Marked ${domain} as safe`);
      await showPopup(`Site ${domain} is safe for studying.`, 'success');
    } else {
      sitesData.blacklist.push(domain);
      activityLog.push({
        tabId,
        url,
        event: 'blacklisted',
        timestamp: new Date().toISOString()
      });
      const prevUrl = previousSafeUrl[tabId] || 'about:blank';
      console.log(`Marked ${domain} as blacklisted, redirecting to ${prevUrl}`);
      await showPopup(`Site ${domain} is not study-related and has been blacklisted.`, 'error');
      await page.goto(prevUrl);
    }
    await saveSites();
    await saveLog();
  }

  // Track navigation
  page.on('load', async () => {
    const tabId = generateTabId(page);
    const url = page.url();
    if (url.startsWith('http')) {
      console.log(`Navigated to ${url}`);
      await trackPageTime(page, tabId);
      await checkAndEvaluateUrl(page, tabId, url);
    }
  });

  // Handle new tabs
  context.on('page', async (newPage) => {
    page = newPage;
    page.on('load', async () => {
      const tabId = generateTabId(page);
      const url = page.url();
      if (url.startsWith('http')) {
        console.log(`New tab navigated to ${url}`);
        await trackPageTime(page, tabId);
        await checkAndEvaluateUrl(page, tabId, url);
      }
    });
  });

  // Keep running until browser closes
  await new Promise((resolve) => {
    browser.on('close', resolve);
  });

  // Clean up
  if (popupSocket) {
    popupSocket.end();
  }
  await saveLog();
  await saveSites();
  await browser.close();
})();